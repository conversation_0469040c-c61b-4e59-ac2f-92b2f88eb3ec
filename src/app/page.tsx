import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen bg-[#0c0c23]">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 lg:px-12">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10">
            <Image
              src="/nexuspoint-logo.svg"
              alt="NexusPoint Logo"
              width={40}
              height={40}
              className="w-full h-full"
            />
          </div>
          <span className="text-white font-bold text-xl">NexusPoint</span>
        </div>
        <div className="hidden md:flex items-center space-x-8">
          <a href="#services" className="text-[#d2d1d2] hover:text-[#33b9ca] transition-colors">Services</a>
          <a href="#about" className="text-[#d2d1d2] hover:text-[#33b9ca] transition-colors">About</a>
          <a href="#contact" className="text-[#d2d1d2] hover:text-[#33b9ca] transition-colors">Contact</a>
          <button className="bg-[#33b9ca] text-white px-6 py-2 rounded-lg hover:bg-[#2f8098] transition-colors">
            Get Started
          </button>
        </div>
      </nav>

      {/* Hero Section */}
      <main className="flex flex-col items-center justify-center min-h-[80vh] px-6 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Logo */}
          <div className="mb-8">
            <div className="w-32 h-32 mx-auto mb-6">
              <Image
                src="/nexuspoint-logo.svg"
                alt="NexusPoint Logo"
                width={128}
                height={128}
                priority
                className="w-full h-full"
              />
            </div>
            <h1 className="text-white text-5xl md:text-7xl font-bold mb-4">
              NEXUSPOINT
            </h1>
            <p className="text-[#33b9ca] text-xl md:text-2xl font-medium tracking-wider">
              WHERE VISION MEETS VELOCITY
            </p>
          </div>

          {/* Hero Content */}
          <div className="mb-12">
            <h2 className="text-[#d2d1d2] text-xl md:text-2xl mb-6 leading-relaxed">
              Transforming businesses through strategic consulting and innovative solutions
            </h2>
            <p className="text-[#726c6e] text-lg mb-8 max-w-2xl mx-auto">
              We accelerate your business growth by connecting visionary strategies with rapid execution,
              delivering measurable results that drive sustainable success.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="bg-[#33b9ca] text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-[#2f8098] transition-colors shadow-lg">
              Start Your Transformation
            </button>
            <button className="border-2 border-[#33b9ca] text-[#33b9ca] px-8 py-4 rounded-lg text-lg font-semibold hover:bg-[#33b9ca] hover:text-white transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </main>

      {/* Features Section */}
      <section className="py-20 px-6 lg:px-12">
        <div className="max-w-6xl mx-auto">
          <h3 className="text-[#33b9ca] text-3xl font-bold text-center mb-12">
            Why Choose NexusPoint?
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-[#383849] p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-[#33b9ca] rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">⚡</span>
              </div>
              <h4 className="text-white text-xl font-semibold mb-4">Rapid Results</h4>
              <p className="text-[#d2d1d2]">
                Fast-track your success with our proven methodologies and agile approach to consulting.
              </p>
            </div>
            <div className="bg-[#383849] p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-[#33b9ca] rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🎯</span>
              </div>
              <h4 className="text-white text-xl font-semibold mb-4">Strategic Vision</h4>
              <p className="text-[#d2d1d2]">
                Clear roadmaps and strategic insights that align with your business objectives.
              </p>
            </div>
            <div className="bg-[#383849] p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-[#33b9ca] rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🚀</span>
              </div>
              <h4 className="text-white text-xl font-semibold mb-4">Innovation Focus</h4>
              <p className="text-[#d2d1d2]">
                Cutting-edge solutions that keep you ahead of the competition.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
