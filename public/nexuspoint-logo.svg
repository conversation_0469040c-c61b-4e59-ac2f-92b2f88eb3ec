<svg width="160" height="80" viewBox="0 0 160 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for 3D diamond effect -->
    <linearGradient id="topFace" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d0d0d0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a0a0a0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="leftFace" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#909090;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#606060;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="rightFace" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#707070;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#404040;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 3D Diamond Shape -->
  <g transform="translate(40,40)">
    <!-- Top face -->
    <path d="M 0,-25 L 20,0 L 0,25 L -20,0 Z" fill="url(#topFace)" stroke="#555" stroke-width="0.5"/>
    <!-- Left face -->
    <path d="M -20,0 L 0,25 L -5,30 L -25,5 Z" fill="url(#leftFace)" stroke="#555" stroke-width="0.5"/>
    <!-- Right face -->
    <path d="M 20,0 L 25,5 L 5,30 L 0,25 Z" fill="url(#rightFace)" stroke="#555" stroke-width="0.5"/>
  </g>

  <!-- Letter N -->
  <text x="100" y="55" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#33b9ca">N</text>
</svg>
