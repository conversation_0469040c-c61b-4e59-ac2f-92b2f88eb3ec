<svg width="200" height="120" viewBox="0 0 200 120" xmlns="http://www.w3.org/2000/svg">
  <!-- Diamond shape background -->
  <g transform="translate(60,60) rotate(45)">
    <rect x="-35" y="-35" width="70" height="70" fill="url(#diamondGradient)" rx="8"/>
  </g>

  <!-- Letter N -->
  <text x="130" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#33b9ca">N</text>

  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a0a0a0;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#726c6e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#383849;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
