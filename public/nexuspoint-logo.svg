<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Diamond shape background -->
  <g transform="translate(100,100) rotate(45)">
    <rect x="-50" y="-50" width="100" height="100" fill="url(#diamondGradient)" rx="15"/>
  </g>
  
  <!-- Letter N -->
  <text x="100" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#33b9ca">N</text>
  
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#726c6e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#383849;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
