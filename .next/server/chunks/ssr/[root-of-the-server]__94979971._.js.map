{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Nexus-point-website/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-[#0c0c23]\">\n      {/* Navigation */}\n      <nav className=\"flex items-center justify-between px-6 py-4 lg:px-12\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-gradient-to-br from-[#33b9ca] to-[#2f8098] rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-lg\">N</span>\n          </div>\n          <span className=\"text-white font-bold text-xl\">NexusPoint</span>\n        </div>\n        <div className=\"hidden md:flex items-center space-x-8\">\n          <a href=\"#services\" className=\"text-[#d2d1d2] hover:text-[#33b9ca] transition-colors\">Services</a>\n          <a href=\"#about\" className=\"text-[#d2d1d2] hover:text-[#33b9ca] transition-colors\">About</a>\n          <a href=\"#contact\" className=\"text-[#d2d1d2] hover:text-[#33b9ca] transition-colors\">Contact</a>\n          <button className=\"bg-[#33b9ca] text-white px-6 py-2 rounded-lg hover:bg-[#2f8098] transition-colors\">\n            Get Started\n          </button>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <main className=\"flex flex-col items-center justify-center min-h-[80vh] px-6 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Logo */}\n          <div className=\"mb-8\">\n            <div className=\"w-32 h-32 mx-auto mb-6\">\n              <Image\n                src=\"/nexuspoint-logo.svg\"\n                alt=\"NexusPoint Logo\"\n                width={128}\n                height={128}\n                priority\n                className=\"w-full h-full\"\n              />\n            </div>\n            <h1 className=\"text-white text-5xl md:text-7xl font-bold mb-4\">\n              NEXUSPOINT\n            </h1>\n            <p className=\"text-[#33b9ca] text-xl md:text-2xl font-medium tracking-wider\">\n              WHERE VISION MEETS VELOCITY\n            </p>\n          </div>\n\n          {/* Hero Content */}\n          <div className=\"mb-12\">\n            <h2 className=\"text-[#d2d1d2] text-xl md:text-2xl mb-6 leading-relaxed\">\n              Transforming businesses through strategic consulting and innovative solutions\n            </h2>\n            <p className=\"text-[#726c6e] text-lg mb-8 max-w-2xl mx-auto\">\n              We accelerate your business growth by connecting visionary strategies with rapid execution,\n              delivering measurable results that drive sustainable success.\n            </p>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <button className=\"bg-[#33b9ca] text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-[#2f8098] transition-colors shadow-lg\">\n              Start Your Transformation\n            </button>\n            <button className=\"border-2 border-[#33b9ca] text-[#33b9ca] px-8 py-4 rounded-lg text-lg font-semibold hover:bg-[#33b9ca] hover:text-white transition-colors\">\n              Learn More\n            </button>\n          </div>\n        </div>\n      </main>\n\n      {/* Features Section */}\n      <section className=\"py-20 px-6 lg:px-12\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h3 className=\"text-[#33b9ca] text-3xl font-bold text-center mb-12\">\n            Why Choose NexusPoint?\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-[#383849] p-8 rounded-xl text-center\">\n              <div className=\"w-16 h-16 bg-[#33b9ca] rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white text-2xl\">⚡</span>\n              </div>\n              <h4 className=\"text-white text-xl font-semibold mb-4\">Rapid Results</h4>\n              <p className=\"text-[#d2d1d2]\">\n                Fast-track your success with our proven methodologies and agile approach to consulting.\n              </p>\n            </div>\n            <div className=\"bg-[#383849] p-8 rounded-xl text-center\">\n              <div className=\"w-16 h-16 bg-[#33b9ca] rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white text-2xl\">🎯</span>\n              </div>\n              <h4 className=\"text-white text-xl font-semibold mb-4\">Strategic Vision</h4>\n              <p className=\"text-[#d2d1d2]\">\n                Clear roadmaps and strategic insights that align with your business objectives.\n              </p>\n            </div>\n            <div className=\"bg-[#383849] p-8 rounded-xl text-center\">\n              <div className=\"w-16 h-16 bg-[#33b9ca] rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white text-2xl\">🚀</span>\n              </div>\n              <h4 className=\"text-white text-xl font-semibold mb-4\">Innovation Focus</h4>\n              <p className=\"text-[#d2d1d2]\">\n                Cutting-edge solutions that keep you ahead of the competition.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;0CAEjD,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;;kCAEjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAwD;;;;;;0CACtF,8OAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAwD;;;;;;0CACnF,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAwD;;;;;;0CACrF,8OAAC;gCAAO,WAAU;0CAAoF;;;;;;;;;;;;;;;;;;0BAO1G,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,8OAAC;oCAAE,WAAU;8CAAgE;;;;;;;;;;;;sCAM/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAO/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAAoH;;;;;;8CAGtI,8OAAC;oCAAO,WAAU;8CAA4I;;;;;;;;;;;;;;;;;;;;;;;0BAQpK,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;sDAExC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;sDAExC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;sDAExC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}]}